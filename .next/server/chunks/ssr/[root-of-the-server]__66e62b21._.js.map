{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/project/html/pnds/src/components/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Dialog, DialogPanel } from '@headlessui/react'\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline'\nimport Image from 'next/image'\nimport Link from 'next/link'\n\nconst navigation = [\n  {\n    name: 'About',\n    href: '/about',\n    children: [\n      { name: 'About Us', href: '/about' },\n      { name: 'PNDS Constitution & Bylaws', href: '/constitution' },\n      { name: 'Executive Committee', href: '/executive-committee' },\n      { name: 'Annual General Body Meetings', href: '/agbm' },\n      { name: 'Code of Ethics', href: '/code-of-ethics' },\n      { name: 'International Affiliations', href: '/international-affiliations' },\n      { name: 'National Collaborators', href: '/national-collaborators' },\n      { name: 'Contact', href: '/contact' },\n    ],\n  },\n  {\n    name: 'Membership',\n    href: '/membership',\n    children: [\n      { name: 'Membership Process', href: '/membership' },\n      { name: 'Registration Exam', href: '/rdn-exam' },\n      { name: 'RD<PERSON>', href: '/rdn-renewal' },\n    ],\n  },\n  {\n    name: 'Activities',\n    href: '/activities',\n    children: [\n      { name: 'Continuing Nutrition Education', href: '/cne' },\n      { name: 'Webinars', href: '/webinars' },\n      { name: 'Workshops/Seminars', href: '/workshops' },\n      { name: 'Conferences', href: '/conferences' },\n    ],\n  },\n  {\n    name: '3rd International Conference 2025',\n    href: '/conference-2025',\n    children: [\n      { name: 'Call for Abstracts', href: '/call-for-abstracts' },\n      { name: 'Call for Workshops', href: '/call-for-workshops' },\n      { name: 'Organizing Committee', href: '/organizing-committee' },\n      { name: 'Sponsorship Opportunities', href: '/sponsorship' },\n      { name: 'Travel Instructions', href: '/travel-instructions' },\n    ],\n  },\n]\n\nexport default function Header() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)\n\n  return (\n    <header className=\"bg-white shadow-sm\">\n      {/* Top bar */}\n      <div className=\"bg-primary text-primary-foreground\">\n        <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex h-10 items-center justify-between text-sm\">\n            <div className=\"flex items-center space-x-4\">\n              <span>📧 <EMAIL></span>\n              <span>📞 +92-21-*********</span>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Link href=\"/member-login\" className=\"hover:text-accent transition-colors\">\n                Register/Login as Member\n              </Link>\n              <Link href=\"/sponsorship\" className=\"hover:text-accent transition-colors\">\n                Sponsorship Opportunities\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main header */}\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-20 items-center justify-between\">\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-3\">\n              <div className=\"h-12 w-12 bg-primary rounded-full flex items-center justify-center\">\n                <span className=\"text-primary-foreground font-bold text-xl\">P</span>\n              </div>\n              <div>\n                <h1 className=\"text-xl font-bold text-foreground\">PNDS</h1>\n                <p className=\"text-sm text-muted-foreground\">Pakistan Nutrition and Dietetic Society</p>\n              </div>\n            </Link>\n          </div>\n\n          {/* Desktop navigation */}\n          <nav className=\"hidden lg:flex lg:space-x-8\">\n            {navigation.map((item) => (\n              <div key={item.name} className=\"relative group\">\n                <Link\n                  href={item.href}\n                  className=\"text-foreground hover:text-primary transition-colors duration-200 font-medium\"\n                >\n                  {item.name}\n                </Link>\n                {item.children && (\n                  <div className=\"absolute left-0 mt-2 w-64 bg-white shadow-lg rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                    <div className=\"py-2\">\n                      {item.children.map((child) => (\n                        <Link\n                          key={child.name}\n                          href={child.href}\n                          className=\"block px-4 py-2 text-sm text-foreground hover:bg-muted hover:text-primary transition-colors\"\n                        >\n                          {child.name}\n                        </Link>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </div>\n            ))}\n          </nav>\n\n          {/* Mobile menu button */}\n          <div className=\"lg:hidden\">\n            <button\n              type=\"button\"\n              className=\"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-foreground\"\n              onClick={() => setMobileMenuOpen(true)}\n            >\n              <span className=\"sr-only\">Open main menu</span>\n              <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      <Dialog className=\"lg:hidden\" open={mobileMenuOpen} onClose={setMobileMenuOpen}>\n        <div className=\"fixed inset-0 z-50\" />\n        <DialogPanel className=\"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\">\n          <div className=\"flex items-center justify-between\">\n            <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n              <span className=\"sr-only\">PNDS</span>\n              <div className=\"h-8 w-8 bg-primary rounded-full flex items-center justify-center\">\n                <span className=\"text-primary-foreground font-bold\">P</span>\n              </div>\n            </Link>\n            <button\n              type=\"button\"\n              className=\"-m-2.5 rounded-md p-2.5 text-foreground\"\n              onClick={() => setMobileMenuOpen(false)}\n            >\n              <span className=\"sr-only\">Close menu</span>\n              <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n            </button>\n          </div>\n          <div className=\"mt-6 flow-root\">\n            <div className=\"-my-6 divide-y divide-gray-500/10\">\n              <div className=\"space-y-2 py-6\">\n                {navigation.map((item) => (\n                  <div key={item.name}>\n                    <Link\n                      href={item.href}\n                      className=\"-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-foreground hover:bg-muted\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      {item.name}\n                    </Link>\n                    {item.children && (\n                      <div className=\"ml-4 space-y-1\">\n                        {item.children.map((child) => (\n                          <Link\n                            key={child.name}\n                            href={child.href}\n                            className=\"-mx-3 block rounded-lg px-3 py-1 text-sm leading-7 text-muted-foreground hover:bg-muted hover:text-foreground\"\n                            onClick={() => setMobileMenuOpen(false)}\n                          >\n                            {child.name}\n                          </Link>\n                        ))}\n                      </div>\n                    )}\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </DialogPanel>\n      </Dialog>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBAAE,MAAM;gBAAY,MAAM;YAAS;YACnC;gBAAE,MAAM;gBAA8B,MAAM;YAAgB;YAC5D;gBAAE,MAAM;gBAAuB,MAAM;YAAuB;YAC5D;gBAAE,MAAM;gBAAgC,MAAM;YAAQ;YACtD;gBAAE,MAAM;gBAAkB,MAAM;YAAkB;YAClD;gBAAE,MAAM;gBAA8B,MAAM;YAA8B;YAC1E;gBAAE,MAAM;gBAA0B,MAAM;YAA0B;YAClE;gBAAE,MAAM;gBAAW,MAAM;YAAW;SACrC;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBAAE,MAAM;gBAAsB,MAAM;YAAc;YAClD;gBAAE,MAAM;gBAAqB,MAAM;YAAY;YAC/C;gBAAE,MAAM;gBAAe,MAAM;YAAe;SAC7C;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBAAE,MAAM;gBAAkC,MAAM;YAAO;YACvD;gBAAE,MAAM;gBAAY,MAAM;YAAY;YACtC;gBAAE,MAAM;gBAAsB,MAAM;YAAa;YACjD;gBAAE,MAAM;gBAAe,MAAM;YAAe;SAC7C;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBAAE,MAAM;gBAAsB,MAAM;YAAsB;YAC1D;gBAAE,MAAM;gBAAsB,MAAM;YAAsB;YAC1D;gBAAE,MAAM;gBAAwB,MAAM;YAAwB;YAC9D;gBAAE,MAAM;gBAA6B,MAAM;YAAe;YAC1D;gBAAE,MAAM;gBAAuB,MAAM;YAAuB;SAC7D;IACH;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAgB,WAAU;kDAAsC;;;;;;kDAG3E,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAe,WAAU;kDAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;kDAE9D,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;sCAMnD,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;oCAAoB,WAAU;;sDAC7B,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAU;sDAET,KAAK,IAAI;;;;;;wCAEX,KAAK,QAAQ,kBACZ,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,sBAClB,8OAAC,4JAAA,CAAA,UAAI;wDAEH,MAAM,MAAM,IAAI;wDAChB,WAAU;kEAET,MAAM,IAAI;uDAJN,MAAM,IAAI;;;;;;;;;;;;;;;;mCAZjB,KAAK,IAAI;;;;;;;;;;sCA2BvB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,kBAAkB;;kDAEjC,8OAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;wCAAU,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOnD,8OAAC,+KAAA,CAAA,SAAM;gBAAC,WAAU;gBAAY,MAAM;gBAAgB,SAAS;;kCAC3D,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC,+KAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAoC;;;;;;;;;;;;;;;;;kDAGxD,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;;0DAEjC,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;gDAAU,eAAY;;;;;;;;;;;;;;;;;;0CAG/C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;;kEACC,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;wDACV,SAAS,IAAM,kBAAkB;kEAEhC,KAAK,IAAI;;;;;;oDAEX,KAAK,QAAQ,kBACZ,8OAAC;wDAAI,WAAU;kEACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,sBAClB,8OAAC,4JAAA,CAAA,UAAI;gEAEH,MAAM,MAAM,IAAI;gEAChB,WAAU;gEACV,SAAS,IAAM,kBAAkB;0EAEhC,MAAM,IAAI;+DALN,MAAM,IAAI;;;;;;;;;;;+CAZf,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BrC", "debugId": null}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/project/html/pnds/src/components/Hero.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { ChevronRightIcon } from '@heroicons/react/20/solid'\nimport { \n  AcademicCapIcon, \n  UserGroupIcon, \n  GlobeAltIcon,\n  HeartIcon \n} from '@heroicons/react/24/outline'\nimport Link from 'next/link'\n\nconst stats = [\n  { id: 1, name: 'Active Members', value: '900+', icon: UserGroupIcon },\n  { id: 2, name: 'Years of Service', value: '15+', icon: AcademicCapIcon },\n  { id: 3, name: 'International Affiliations', value: '5+', icon: GlobeAltIcon },\n  { id: 4, name: 'Lives Impacted', value: '10K+', icon: HeartIcon },\n]\n\nexport default function Hero() {\n  return (\n    <div className=\"relative isolate overflow-hidden bg-gradient-to-br from-primary/5 via-white to-accent/5\">\n      {/* Background pattern */}\n      <div className=\"absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80\" aria-hidden=\"true\">\n        <div\n          className=\"relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-primary to-accent opacity-20 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]\"\n          style={{\n            clipPath:\n              'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',\n          }}\n        />\n      </div>\n\n      <div className=\"mx-auto max-w-7xl px-6 pb-24 pt-10 sm:pb-32 lg:flex lg:px-8 lg:py-40\">\n        <div className=\"mx-auto max-w-2xl lg:mx-0 lg:max-w-xl lg:flex-shrink-0 lg:pt-8\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n          >\n            <div className=\"mt-24 sm:mt-32 lg:mt-16\">\n              <Link href=\"/conference-2025\" className=\"inline-flex space-x-6\">\n                <span className=\"rounded-full bg-primary/10 px-3 py-1 text-sm font-semibold leading-6 text-primary ring-1 ring-inset ring-primary/10\">\n                  What's new\n                </span>\n                <span className=\"inline-flex items-center space-x-2 text-sm font-medium leading-6 text-muted-foreground\">\n                  <span>3rd International Conference 2025</span>\n                  <ChevronRightIcon className=\"h-5 w-5 text-muted-foreground\" aria-hidden=\"true\" />\n                </span>\n              </Link>\n            </div>\n            \n            <h1 className=\"mt-10 text-4xl font-bold tracking-tight text-foreground sm:text-6xl\">\n              Pakistan Nutrition and Dietetic Society\n            </h1>\n            \n            <p className=\"mt-6 text-lg leading-8 text-muted-foreground\">\n              PNDS is the only professional nutrition and dietetic society in Pakistan with over 900 members. \n              We lead the way to better nutrition through education, training, and research to promote the \n              nutritional well-being of the Pakistani population.\n            </p>\n            \n            <div className=\"mt-10 flex items-center gap-x-6\">\n              <Link\n                href=\"/membership\"\n                className=\"rounded-md bg-primary px-3.5 py-2.5 text-sm font-semibold text-primary-foreground shadow-sm hover:bg-primary/90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary transition-colors\"\n              >\n                Become a Member\n              </Link>\n              <Link \n                href=\"/about\" \n                className=\"text-sm font-semibold leading-6 text-foreground hover:text-primary transition-colors\"\n              >\n                Learn more <span aria-hidden=\"true\">→</span>\n              </Link>\n            </div>\n          </motion.div>\n        </div>\n\n        <div className=\"mx-auto mt-16 flex max-w-2xl sm:mt-24 lg:ml-10 lg:mr-0 lg:mt-0 lg:max-w-none lg:flex-none xl:ml-32\">\n          <div className=\"max-w-3xl flex-none sm:max-w-5xl lg:max-w-none\">\n            <motion.div\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.8, delay: 0.2 }}\n              className=\"relative\"\n            >\n              <div className=\"w-[76rem] rounded-md bg-white/5 ring-1 ring-white/10 backdrop-blur\">\n                <div className=\"relative rounded-md bg-gradient-to-br from-primary/10 to-accent/10 p-8\">\n                  <div className=\"grid grid-cols-2 gap-8 sm:grid-cols-4\">\n                    {stats.map((stat, index) => (\n                      <motion.div\n                        key={stat.id}\n                        initial={{ opacity: 0, y: 20 }}\n                        animate={{ opacity: 1, y: 0 }}\n                        transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}\n                        className=\"text-center\"\n                      >\n                        <div className=\"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10\">\n                          <stat.icon className=\"h-6 w-6 text-primary\" aria-hidden=\"true\" />\n                        </div>\n                        <div className=\"text-2xl font-bold text-foreground\">{stat.value}</div>\n                        <div className=\"text-sm text-muted-foreground\">{stat.name}</div>\n                      </motion.div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </div>\n\n      {/* Bottom gradient */}\n      <div\n        className=\"absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]\"\n        aria-hidden=\"true\"\n      >\n        <div\n          className=\"relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-accent to-primary opacity-20 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]\"\n          style={{\n            clipPath:\n              'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',\n          }}\n        />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAMA;AAVA;;;;;;AAYA,MAAM,QAAQ;IACZ;QAAE,IAAI;QAAG,MAAM;QAAkB,OAAO;QAAQ,MAAM,yNAAA,CAAA,gBAAa;IAAC;IACpE;QAAE,IAAI;QAAG,MAAM;QAAoB,OAAO;QAAO,MAAM,6NAAA,CAAA,kBAAe;IAAC;IACvE;QAAE,IAAI;QAAG,MAAM;QAA8B,OAAO;QAAM,MAAM,uNAAA,CAAA,eAAY;IAAC;IAC7E;QAAE,IAAI;QAAG,MAAM;QAAkB,OAAO;QAAQ,MAAM,iNAAA,CAAA,YAAS;IAAC;CACjE;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;gBAAqF,eAAY;0BAC9G,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBACL,UACE;oBACJ;;;;;;;;;;;0BAIJ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;;8CAE5B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAmB,WAAU;;0DACtC,8OAAC;gDAAK,WAAU;0DAAsH;;;;;;0DAGtI,8OAAC;gDAAK,WAAU;;kEACd,8OAAC;kEAAK;;;;;;kEACN,8OAAC,6NAAA,CAAA,mBAAgB;wDAAC,WAAU;wDAAgC,eAAY;;;;;;;;;;;;;;;;;;;;;;;8CAK9E,8OAAC;oCAAG,WAAU;8CAAsE;;;;;;8CAIpF,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;8CAM5D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;gDACX;8DACY,8OAAC;oDAAK,eAAY;8DAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM5C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,YAAY;wDAAE,UAAU;wDAAK,OAAO,MAAM,QAAQ;oDAAI;oDACtD,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,KAAK,IAAI;gEAAC,WAAU;gEAAuB,eAAY;;;;;;;;;;;sEAE1D,8OAAC;4DAAI,WAAU;sEAAsC,KAAK,KAAK;;;;;;sEAC/D,8OAAC;4DAAI,WAAU;sEAAiC,KAAK,IAAI;;;;;;;mDAVpD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAsB9B,8OAAC;gBACC,WAAU;gBACV,eAAY;0BAEZ,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBACL,UACE;oBACJ;;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 890, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/project/html/pnds/src/components/MissionVision.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { \n  EyeIcon, \n  HeartIcon, \n  StarIcon,\n  AcademicCapIcon,\n  UserGroupIcon,\n  GlobeAltIcon\n} from '@heroicons/react/24/outline'\n\nconst values = [\n  {\n    name: 'Integrity',\n    description: 'We maintain the highest standards of professional ethics and honesty in all our endeavors.',\n    icon: HeartIcon,\n  },\n  {\n    name: 'Quality',\n    description: 'We are committed to excellence in education, training, and professional development.',\n    icon: StarIcon,\n  },\n  {\n    name: 'Inclusiveness',\n    description: 'We welcome and value diversity, ensuring equal opportunities for all members.',\n    icon: UserGroupIcon,\n  },\n  {\n    name: 'Education',\n    description: 'We promote continuous learning and knowledge sharing among nutrition professionals.',\n    icon: AcademicCapIcon,\n  },\n  {\n    name: 'Collaboration',\n    description: 'We foster partnerships with national and international organizations.',\n    icon: GlobeAltIcon,\n  },\n  {\n    name: 'Ethical Practice',\n    description: 'We uphold the highest standards of professional conduct and ethical practice.',\n    icon: EyeIcon,\n  },\n]\n\nexport default function MissionVision() {\n  return (\n    <div className=\"bg-white py-24 sm:py-32\">\n      <div className=\"mx-auto max-w-7xl px-6 lg:px-8\">\n        {/* Mission Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"mx-auto max-w-2xl lg:text-center mb-16\"\n        >\n          <h2 className=\"text-base font-semibold leading-7 text-primary\">Our Purpose</h2>\n          <p className=\"mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl\">\n            Our Mission\n          </p>\n          <p className=\"mt-6 text-lg leading-8 text-muted-foreground\">\n            PNDS will provide a platform for collective action through educated, skilled and trained \n            professionals who will assume consultative and leadership roles in academia, health-related \n            institutions, and corporate sectors. This shall be achieved through capacity building and \n            accreditation of professionals in nutrition; networking and partnership with the government, \n            national and international institutions; public awareness; and advocacy.\n          </p>\n        </motion.div>\n\n        {/* Vision Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.2 }}\n          viewport={{ once: true }}\n          className=\"mx-auto max-w-2xl lg:text-center mb-16\"\n        >\n          <h2 className=\"text-base font-semibold leading-7 text-primary\">Our Future</h2>\n          <p className=\"mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl\">\n            Our Vision\n          </p>\n          <p className=\"mt-6 text-lg leading-8 text-muted-foreground\">\n            PNDS is a National Professional Society that aims to develop and strengthen the profession \n            of Nutrition and Dietetics. PNDS targets to achieve this through education, training, and \n            research in order to promote the nutritional well-being of the Pakistani Population.\n          </p>\n        </motion.div>\n\n        {/* Values Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"mx-auto max-w-2xl lg:text-center mb-16\"\n        >\n          <h2 className=\"text-base font-semibold leading-7 text-primary\">What We Stand For</h2>\n          <p className=\"mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl\">\n            Our Values\n          </p>\n          <p className=\"mt-6 text-lg leading-8 text-muted-foreground\">\n            PNDS espouses the core values of integrity, quality, inclusiveness, freedom of expression, \n            respect for all, and ethical practice in everything it does.\n          </p>\n        </motion.div>\n\n        {/* Values Grid */}\n        <div className=\"mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none\">\n          <dl className=\"grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3\">\n            {values.map((value, index) => (\n              <motion.div\n                key={value.name}\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"flex flex-col\"\n              >\n                <dt className=\"flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground\">\n                  <div className=\"h-10 w-10 flex items-center justify-center rounded-lg bg-primary/10\">\n                    <value.icon className=\"h-5 w-5 text-primary\" aria-hidden=\"true\" />\n                  </div>\n                  {value.name}\n                </dt>\n                <dd className=\"mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground\">\n                  <p className=\"flex-auto\">{value.description}</p>\n                </dd>\n              </motion.div>\n            ))}\n          </dl>\n        </div>\n\n        {/* Call to Action */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.8 }}\n          viewport={{ once: true }}\n          className=\"mt-16 flex justify-center\"\n        >\n          <div className=\"relative rounded-2xl bg-gradient-to-r from-primary to-accent p-8 text-center\">\n            <h3 className=\"text-2xl font-bold text-white mb-4\">\n              Join Our Mission\n            </h3>\n            <p className=\"text-white/90 mb-6 max-w-md\">\n              Be part of Pakistan's leading nutrition and dietetic society. Together, we can improve \n              the nutritional well-being of our nation.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <a\n                href=\"/membership\"\n                className=\"rounded-md bg-white px-6 py-3 text-sm font-semibold text-primary shadow-sm hover:bg-white/90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white transition-colors\"\n              >\n                Become a Member\n              </a>\n              <a\n                href=\"/about\"\n                className=\"rounded-md border border-white px-6 py-3 text-sm font-semibold text-white hover:bg-white/10 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white transition-colors\"\n              >\n                Learn More\n              </a>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAYA,MAAM,SAAS;IACb;QACE,MAAM;QACN,aAAa;QACb,MAAM,iNAAA,CAAA,YAAS;IACjB;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,+MAAA,CAAA,WAAQ;IAChB;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,yNAAA,CAAA,gBAAa;IACrB;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,6NAAA,CAAA,kBAAe;IACvB;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,uNAAA,CAAA,eAAY;IACpB;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,6MAAA,CAAA,UAAO;IACf;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAC/D,8OAAC;4BAAE,WAAU;sCAAqE;;;;;;sCAGlF,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;;;;;;;8BAU9D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAC/D,8OAAC;4BAAE,WAAU;sCAAqE;;;;;;sCAGlF,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;;;;;;;8BAQ9D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAC/D,8OAAC;4BAAE,WAAU;sCAAqE;;;;;;sCAGlF,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;;;;;;;8BAO9D,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCACX,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,MAAM,QAAQ;gCAAI;gCACtD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,MAAM,IAAI;oDAAC,WAAU;oDAAuB,eAAY;;;;;;;;;;;4CAE1D,MAAM,IAAI;;;;;;;kDAEb,8OAAC;wCAAG,WAAU;kDACZ,cAAA,8OAAC;4CAAE,WAAU;sDAAa,MAAM,WAAW;;;;;;;;;;;;+BAdxC,MAAM,IAAI;;;;;;;;;;;;;;;8BAsBvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqC;;;;;;0CAGnD,8OAAC;gCAAE,WAAU;0CAA8B;;;;;;0CAI3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}