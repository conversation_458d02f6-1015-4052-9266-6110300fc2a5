{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/project/html/pnds/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/project/html/pnds/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/project/html/pnds/src/components/Hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Hero.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Hero.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/project/html/pnds/src/components/Hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Hero.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Hero.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuQ,GACpS,qCACA", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/project/html/pnds/src/components/MissionVision.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/MissionVision.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/MissionVision.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/project/html/pnds/src/components/MissionVision.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/MissionVision.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/MissionVision.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/project/html/pnds/src/components/Footer.tsx"], "sourcesContent": ["import Link from 'next/link'\nimport { \n  EnvelopeIcon, \n  PhoneIcon, \n  MapPinIcon \n} from '@heroicons/react/24/outline'\n\nconst navigation = {\n  about: [\n    { name: 'About Us', href: '/about' },\n    { name: 'Executive Committee', href: '/executive-committee' },\n    { name: 'Constitution & Bylaws', href: '/constitution' },\n    { name: 'Code of Ethics', href: '/code-of-ethics' },\n  ],\n  membership: [\n    { name: 'Membership Process', href: '/membership' },\n    { name: 'RDN Exam', href: '/rdn-exam' },\n    { name: 'RDN Renewal', href: '/rdn-renewal' },\n    { name: 'Member Lo<PERSON>', href: '/member-login' },\n  ],\n  activities: [\n    { name: 'Continuing Education', href: '/cne' },\n    { name: 'Webinars', href: '/webinars' },\n    { name: 'Workshops', href: '/workshops' },\n    { name: 'Conferences', href: '/conferences' },\n  ],\n  resources: [\n    { name: 'Food Fact Sheets', href: '/food-facts' },\n    { name: 'Diet Handouts', href: '/diet-handouts' },\n    { name: 'Research Publications', href: '/publications' },\n    { name: 'Guidelines', href: '/guidelines' },\n  ],\n  affiliations: [\n    { name: 'ICDA', href: '/international-affiliations' },\n    { name: 'AFDA', href: '/international-affiliations' },\n    { name: 'IUNS', href: '/international-affiliations' },\n    { name: 'The Nutrition Society UK', href: '/international-affiliations' },\n  ],\n}\n\nconst socialLinks = [\n  {\n    name: 'Facebook',\n    href: '#',\n    icon: (props: any) => (\n      <svg fill=\"currentColor\" viewBox=\"0 0 24 24\" {...props}>\n        <path\n          fillRule=\"evenodd\"\n          d=\"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\"\n          clipRule=\"evenodd\"\n        />\n      </svg>\n    ),\n  },\n  {\n    name: 'Twitter',\n    href: '#',\n    icon: (props: any) => (\n      <svg fill=\"currentColor\" viewBox=\"0 0 24 24\" {...props}>\n        <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\" />\n      </svg>\n    ),\n  },\n  {\n    name: 'Instagram',\n    href: '#',\n    icon: (props: any) => (\n      <svg fill=\"currentColor\" viewBox=\"0 0 24 24\" {...props}>\n        <path\n          fillRule=\"evenodd\"\n          d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.014 5.367 18.647.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297zm7.718-1.297c-.875.807-2.026 1.297-3.323 1.297s-2.448-.49-3.323-1.297c-.807-.875-1.297-2.026-1.297-3.323s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323z\"\n          clipRule=\"evenodd\"\n        />\n      </svg>\n    ),\n  },\n  {\n    name: 'YouTube',\n    href: '#',\n    icon: (props: any) => (\n      <svg fill=\"currentColor\" viewBox=\"0 0 24 24\" {...props}>\n        <path\n          fillRule=\"evenodd\"\n          d=\"M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z\"\n          clipRule=\"evenodd\"\n        />\n      </svg>\n    ),\n  },\n]\n\nexport default function Footer() {\n  return (\n    <footer className=\"bg-gray-900\" aria-labelledby=\"footer-heading\">\n      <h2 id=\"footer-heading\" className=\"sr-only\">\n        Footer\n      </h2>\n      <div className=\"mx-auto max-w-7xl px-6 pb-8 pt-16 sm:pt-24 lg:px-8 lg:pt-32\">\n        <div className=\"xl:grid xl:grid-cols-3 xl:gap-8\">\n          <div className=\"space-y-8\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"h-10 w-10 bg-primary rounded-full flex items-center justify-center\">\n                <span className=\"text-primary-foreground font-bold text-xl\">P</span>\n              </div>\n              <div>\n                <h3 className=\"text-xl font-bold text-white\">PNDS</h3>\n                <p className=\"text-sm text-gray-300\">Pakistan Nutrition and Dietetic Society</p>\n              </div>\n            </div>\n            <p className=\"text-sm leading-6 text-gray-300\">\n              The only professional nutrition and dietetic society in Pakistan, dedicated to promoting \n              the nutritional well-being of the Pakistani population through education, training, and research.\n            </p>\n            <div className=\"flex space-x-6\">\n              {socialLinks.map((item) => (\n                <a key={item.name} href={item.href} className=\"text-gray-400 hover:text-gray-300\">\n                  <span className=\"sr-only\">{item.name}</span>\n                  <item.icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                </a>\n              ))}\n            </div>\n          </div>\n          <div className=\"mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0\">\n            <div className=\"md:grid md:grid-cols-2 md:gap-8\">\n              <div>\n                <h3 className=\"text-sm font-semibold leading-6 text-white\">About</h3>\n                <ul role=\"list\" className=\"mt-6 space-y-4\">\n                  {navigation.about.map((item) => (\n                    <li key={item.name}>\n                      <Link href={item.href} className=\"text-sm leading-6 text-gray-300 hover:text-white\">\n                        {item.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n              <div className=\"mt-10 md:mt-0\">\n                <h3 className=\"text-sm font-semibold leading-6 text-white\">Membership</h3>\n                <ul role=\"list\" className=\"mt-6 space-y-4\">\n                  {navigation.membership.map((item) => (\n                    <li key={item.name}>\n                      <Link href={item.href} className=\"text-sm leading-6 text-gray-300 hover:text-white\">\n                        {item.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            </div>\n            <div className=\"md:grid md:grid-cols-2 md:gap-8\">\n              <div>\n                <h3 className=\"text-sm font-semibold leading-6 text-white\">Activities</h3>\n                <ul role=\"list\" className=\"mt-6 space-y-4\">\n                  {navigation.activities.map((item) => (\n                    <li key={item.name}>\n                      <Link href={item.href} className=\"text-sm leading-6 text-gray-300 hover:text-white\">\n                        {item.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n              <div className=\"mt-10 md:mt-0\">\n                <h3 className=\"text-sm font-semibold leading-6 text-white\">Resources</h3>\n                <ul role=\"list\" className=\"mt-6 space-y-4\">\n                  {navigation.resources.map((item) => (\n                    <li key={item.name}>\n                      <Link href={item.href} className=\"text-sm leading-6 text-gray-300 hover:text-white\">\n                        {item.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Contact Information */}\n        <div className=\"mt-16 border-t border-gray-700 pt-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"flex items-center space-x-3\">\n              <EnvelopeIcon className=\"h-5 w-5 text-primary\" />\n              <div>\n                <p className=\"text-sm font-medium text-white\">Email</p>\n                <p className=\"text-sm text-gray-300\"><EMAIL></p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-3\">\n              <PhoneIcon className=\"h-5 w-5 text-primary\" />\n              <div>\n                <p className=\"text-sm font-medium text-white\">Phone</p>\n                <p className=\"text-sm text-gray-300\">+92-21-123456789</p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-3\">\n              <MapPinIcon className=\"h-5 w-5 text-primary\" />\n              <div>\n                <p className=\"text-sm font-medium text-white\">Address</p>\n                <p className=\"text-sm text-gray-300\">Karachi, Pakistan</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* International Affiliations */}\n        <div className=\"mt-8 border-t border-gray-700 pt-8\">\n          <h3 className=\"text-sm font-semibold leading-6 text-white mb-4\">International Affiliations</h3>\n          <div className=\"flex flex-wrap gap-4\">\n            {navigation.affiliations.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-sm text-gray-300 hover:text-white border border-gray-700 rounded-md px-3 py-1 hover:border-gray-600 transition-colors\"\n              >\n                {item.name}\n              </Link>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"mt-8 border-t border-gray-700 pt-8 flex flex-col md:flex-row justify-between items-center\">\n          <p className=\"text-xs leading-5 text-gray-400\">\n            &copy; 2025 Pakistan Nutrition and Dietetic Society. All rights reserved.\n          </p>\n          <div className=\"mt-4 md:mt-0 flex space-x-6\">\n            <Link href=\"/privacy\" className=\"text-xs leading-5 text-gray-400 hover:text-gray-300\">\n              Privacy Policy\n            </Link>\n            <Link href=\"/terms\" className=\"text-xs leading-5 text-gray-400 hover:text-gray-300\">\n              Terms of Service\n            </Link>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;;;;AAMA,MAAM,aAAa;IACjB,OAAO;QACL;YAAE,MAAM;YAAY,MAAM;QAAS;QACnC;YAAE,MAAM;YAAuB,MAAM;QAAuB;QAC5D;YAAE,MAAM;YAAyB,MAAM;QAAgB;QACvD;YAAE,MAAM;YAAkB,MAAM;QAAkB;KACnD;IACD,YAAY;QACV;YAAE,MAAM;YAAsB,MAAM;QAAc;QAClD;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAe,MAAM;QAAe;QAC5C;YAAE,MAAM;YAAgB,MAAM;QAAgB;KAC/C;IACD,YAAY;QACV;YAAE,MAAM;YAAwB,MAAM;QAAO;QAC7C;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAa,MAAM;QAAa;QACxC;YAAE,MAAM;YAAe,MAAM;QAAe;KAC7C;IACD,WAAW;QACT;YAAE,MAAM;YAAoB,MAAM;QAAc;QAChD;YAAE,MAAM;YAAiB,MAAM;QAAiB;QAChD;YAAE,MAAM;YAAyB,MAAM;QAAgB;QACvD;YAAE,MAAM;YAAc,MAAM;QAAc;KAC3C;IACD,cAAc;QACZ;YAAE,MAAM;YAAQ,MAAM;QAA8B;QACpD;YAAE,MAAM;YAAQ,MAAM;QAA8B;QACpD;YAAE,MAAM;YAAQ,MAAM;QAA8B;QACpD;YAAE,MAAM;YAA4B,MAAM;QAA8B;KACzE;AACH;AAEA,MAAM,cAAc;IAClB;QACE,MAAM;QACN,MAAM;QACN,MAAM,CAAC,sBACL,8OAAC;gBAAI,MAAK;gBAAe,SAAQ;gBAAa,GAAG,KAAK;0BACpD,cAAA,8OAAC;oBACC,UAAS;oBACT,GAAE;oBACF,UAAS;;;;;;;;;;;IAIjB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,CAAC,sBACL,8OAAC;gBAAI,MAAK;gBAAe,SAAQ;gBAAa,GAAG,KAAK;0BACpD,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;IAGd;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,CAAC,sBACL,8OAAC;gBAAI,MAAK;gBAAe,SAAQ;gBAAa,GAAG,KAAK;0BACpD,cAAA,8OAAC;oBACC,UAAS;oBACT,GAAE;oBACF,UAAS;;;;;;;;;;;IAIjB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,CAAC,sBACL,8OAAC;gBAAI,MAAK;gBAAe,SAAQ;gBAAa,GAAG,KAAK;0BACpD,cAAA,8OAAC;oBACC,UAAS;oBACT,GAAE;oBACF,UAAS;;;;;;;;;;;IAIjB;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAO,WAAU;QAAc,mBAAgB;;0BAC9C,8OAAC;gBAAG,IAAG;gBAAiB,WAAU;0BAAU;;;;;;0BAG5C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;0DAE9D,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA+B;;;;;;kEAC7C,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAGzC,8OAAC;wCAAE,WAAU;kDAAkC;;;;;;kDAI/C,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC;gDAAkB,MAAM,KAAK,IAAI;gDAAE,WAAU;;kEAC5C,8OAAC;wDAAK,WAAU;kEAAW,KAAK,IAAI;;;;;;kEACpC,8OAAC,KAAK,IAAI;wDAAC,WAAU;wDAAU,eAAY;;;;;;;+CAFrC,KAAK,IAAI;;;;;;;;;;;;;;;;0CAOvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,MAAK;wDAAO,WAAU;kEACvB,WAAW,KAAK,CAAC,GAAG,CAAC,CAAC,qBACrB,8OAAC;0EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAM,KAAK,IAAI;oEAAE,WAAU;8EAC9B,KAAK,IAAI;;;;;;+DAFL,KAAK,IAAI;;;;;;;;;;;;;;;;0DAQxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,MAAK;wDAAO,WAAU;kEACvB,WAAW,UAAU,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC;0EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAM,KAAK,IAAI;oEAAE,WAAU;8EAC9B,KAAK,IAAI;;;;;;+DAFL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;kDAS1B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,MAAK;wDAAO,WAAU;kEACvB,WAAW,UAAU,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC;0EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAM,KAAK,IAAI;oEAAE,WAAU;8EAC9B,KAAK,IAAI;;;;;;+DAFL,KAAK,IAAI;;;;;;;;;;;;;;;;0DAQxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,MAAK;wDAAO,WAAU;kEACvB,WAAW,SAAS,CAAC,GAAG,CAAC,CAAC,qBACzB,8OAAC;0EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAM,KAAK,IAAI;oEAAE,WAAU;8EAC9B,KAAK,IAAI;;;;;;+DAFL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAa9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,uNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;sDACxB,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAiC;;;;;;8DAC9C,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAiC;;;;;;8DAC9C,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,mNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAiC;;;;;;8DAC9C,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO7C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAkD;;;;;;0CAChE,8OAAC;gCAAI,WAAU;0CACZ,WAAW,YAAY,CAAC,GAAG,CAAC,CAAC,qBAC5B,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,IAAI;uCAJL,KAAK,IAAI;;;;;;;;;;;;;;;;kCAUtB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAkC;;;;;;0CAG/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAsD;;;;;;kDAGtF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAsD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhG", "debugId": null}}, {"offset": {"line": 844, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/project/html/pnds/src/app/page.tsx"], "sourcesContent": ["import Header from '@/components/Header'\nimport Hero from '@/components/Hero'\nimport MissionVision from '@/components/MissionVision'\nimport Footer from '@/components/Footer'\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Header />\n      <main>\n        <Hero />\n        <MissionVision />\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BACP,8OAAC;;kCACC,8OAAC,0HAAA,CAAA,UAAI;;;;;kCACL,8OAAC,mIAAA,CAAA,UAAa;;;;;;;;;;;0BAEhB,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}]}