import type { Metadata } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "PNDS - Pakistan Nutrition and Dietetic Society",
  description: "The only professional nutrition and dietetic society in Pakistan with over 900 members. Leading the way to better nutrition through education, training, and research.",
  keywords: ["nutrition", "dietetics", "Pakistan", "health", "professional society", "RDN", "registered dietitian"],
  authors: [{ name: "PNDS" }],
  creator: "Pakistan Nutrition and Dietetic Society",
  publisher: "PNDS",
  openGraph: {
    title: "PNDS - Pakistan Nutrition and Dietetic Society",
    description: "The only professional nutrition and dietetic society in Pakistan with over 900 members.",
    url: "https://pnds.org",
    siteName: "PNDS",
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "PNDS - Pakistan Nutrition and Dietetic Society",
    description: "The only professional nutrition and dietetic society in Pakistan with over 900 members.",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
