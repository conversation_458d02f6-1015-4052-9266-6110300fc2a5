@import "tailwindcss";

:root {
  /* PNDS Brand Colors */
  --primary: 34 139 34; /* Forest Green */
  --primary-foreground: 255 255 255;
  --secondary: 46 125 50; /* Darker Green */
  --secondary-foreground: 255 255 255;
  --accent: 76 175 80; /* Light Green */
  --accent-foreground: 255 255 255;
  --muted: 245 245 245;
  --muted-foreground: 115 115 115;
  --background: 255 255 255;
  --foreground: 33 33 33;
  --card: 255 255 255;
  --card-foreground: 33 33 33;
  --border: 229 229 229;
  --input: 229 229 229;
  --ring: 34 139 34;
}

@media (prefers-color-scheme: dark) {
  :root {
    --primary: 76 175 80;
    --primary-foreground: 0 0 0;
    --secondary: 46 125 50;
    --secondary-foreground: 255 255 255;
    --accent: 34 139 34;
    --accent-foreground: 255 255 255;
    --muted: 38 38 38;
    --muted-foreground: 163 163 163;
    --background: 10 10 10;
    --foreground: 250 250 250;
    --card: 38 38 38;
    --card-foreground: 250 250 250;
    --border: 38 38 38;
    --input: 38 38 38;
    --ring: 76 175 80;
  }
}

@theme inline {
  --color-primary: rgb(var(--primary));
  --color-primary-foreground: rgb(var(--primary-foreground));
  --color-secondary: rgb(var(--secondary));
  --color-secondary-foreground: rgb(var(--secondary-foreground));
  --color-accent: rgb(var(--accent));
  --color-accent-foreground: rgb(var(--accent-foreground));
  --color-muted: rgb(var(--muted));
  --color-muted-foreground: rgb(var(--muted-foreground));
  --color-background: rgb(var(--background));
  --color-foreground: rgb(var(--foreground));
  --color-card: rgb(var(--card));
  --color-card-foreground: rgb(var(--card-foreground));
  --color-border: rgb(var(--border));
  --color-input: rgb(var(--input));
  --color-ring: rgb(var(--ring));
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: rgb(var(--background));
  color: rgb(var(--foreground));
  font-family: var(--font-geist-sans), system-ui, -apple-system, sans-serif;
  font-feature-settings: "rlig" 1, "calt" 1;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  color: rgb(var(--foreground));
}

h1 {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

@media (min-width: 1024px) {
  h1 {
    font-size: 3rem;
    line-height: 1;
  }
}

h2 {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

@media (min-width: 1024px) {
  h2 {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

h3 {
  font-size: 1.5rem;
  line-height: 2rem;
}

@media (min-width: 1024px) {
  h3 {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}
