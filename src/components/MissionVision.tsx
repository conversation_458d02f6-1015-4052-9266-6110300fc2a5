'use client'

import { motion } from 'framer-motion'
import { 
  EyeIcon, 
  HeartIcon, 
  StarIcon,
  AcademicCapIcon,
  UserGroupIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline'

const values = [
  {
    name: 'Integrity',
    description: 'We maintain the highest standards of professional ethics and honesty in all our endeavors.',
    icon: HeartIcon,
  },
  {
    name: 'Quality',
    description: 'We are committed to excellence in education, training, and professional development.',
    icon: StarIcon,
  },
  {
    name: 'Inclusiveness',
    description: 'We welcome and value diversity, ensuring equal opportunities for all members.',
    icon: UserGroupIcon,
  },
  {
    name: 'Education',
    description: 'We promote continuous learning and knowledge sharing among nutrition professionals.',
    icon: AcademicCapIcon,
  },
  {
    name: 'Collaboration',
    description: 'We foster partnerships with national and international organizations.',
    icon: GlobeAltIcon,
  },
  {
    name: 'Ethical Practice',
    description: 'We uphold the highest standards of professional conduct and ethical practice.',
    icon: EyeIcon,
  },
]

export default function MissionVision() {
  return (
    <div className="bg-white py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        {/* Mission Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mx-auto max-w-2xl lg:text-center mb-16"
        >
          <h2 className="text-base font-semibold leading-7 text-primary">Our Purpose</h2>
          <p className="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
            Our Mission
          </p>
          <p className="mt-6 text-lg leading-8 text-muted-foreground">
            PNDS will provide a platform for collective action through educated, skilled and trained 
            professionals who will assume consultative and leadership roles in academia, health-related 
            institutions, and corporate sectors. This shall be achieved through capacity building and 
            accreditation of professionals in nutrition; networking and partnership with the government, 
            national and international institutions; public awareness; and advocacy.
          </p>
        </motion.div>

        {/* Vision Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="mx-auto max-w-2xl lg:text-center mb-16"
        >
          <h2 className="text-base font-semibold leading-7 text-primary">Our Future</h2>
          <p className="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
            Our Vision
          </p>
          <p className="mt-6 text-lg leading-8 text-muted-foreground">
            PNDS is a National Professional Society that aims to develop and strengthen the profession 
            of Nutrition and Dietetics. PNDS targets to achieve this through education, training, and 
            research in order to promote the nutritional well-being of the Pakistani Population.
          </p>
        </motion.div>

        {/* Values Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="mx-auto max-w-2xl lg:text-center mb-16"
        >
          <h2 className="text-base font-semibold leading-7 text-primary">What We Stand For</h2>
          <p className="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
            Our Values
          </p>
          <p className="mt-6 text-lg leading-8 text-muted-foreground">
            PNDS espouses the core values of integrity, quality, inclusiveness, freedom of expression, 
            respect for all, and ethical practice in everything it does.
          </p>
        </motion.div>

        {/* Values Grid */}
        <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
          <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
            {values.map((value, index) => (
              <motion.div
                key={value.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
                viewport={{ once: true }}
                className="flex flex-col"
              >
                <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <div className="h-10 w-10 flex items-center justify-center rounded-lg bg-primary/10">
                    <value.icon className="h-5 w-5 text-primary" aria-hidden="true" />
                  </div>
                  {value.name}
                </dt>
                <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p className="flex-auto">{value.description}</p>
                </dd>
              </motion.div>
            ))}
          </dl>
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          viewport={{ once: true }}
          className="mt-16 flex justify-center"
        >
          <div className="relative rounded-2xl bg-gradient-to-r from-primary to-accent p-8 text-center">
            <h3 className="text-2xl font-bold text-white mb-4">
              Join Our Mission
            </h3>
            <p className="text-white/90 mb-6 max-w-md">
              Be part of Pakistan's leading nutrition and dietetic society. Together, we can improve 
              the nutritional well-being of our nation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/membership"
                className="rounded-md bg-white px-6 py-3 text-sm font-semibold text-primary shadow-sm hover:bg-white/90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white transition-colors"
              >
                Become a Member
              </a>
              <a
                href="/about"
                className="rounded-md border border-white px-6 py-3 text-sm font-semibold text-white hover:bg-white/10 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white transition-colors"
              >
                Learn More
              </a>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
