'use client'

import { motion } from 'framer-motion'
import { ChevronRightIcon } from '@heroicons/react/20/solid'
import { 
  AcademicCapIcon, 
  UserGroupIcon, 
  GlobeAltIcon,
  HeartIcon 
} from '@heroicons/react/24/outline'
import Link from 'next/link'

const stats = [
  { id: 1, name: 'Active Members', value: '900+', icon: UserGroupIcon },
  { id: 2, name: 'Years of Service', value: '15+', icon: AcademicCapIcon },
  { id: 3, name: 'International Affiliations', value: '5+', icon: GlobeAltIcon },
  { id: 4, name: 'Lives Impacted', value: '10K+', icon: HeartIcon },
]

export default function Hero() {
  return (
    <div className="relative isolate overflow-hidden bg-gradient-to-br from-primary/5 via-white to-accent/5">
      {/* Background pattern */}
      <div className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80" aria-hidden="true">
        <div
          className="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-primary to-accent opacity-20 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
          style={{
            clipPath:
              'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
          }}
        />
      </div>

      <div className="mx-auto max-w-7xl px-6 pb-24 pt-10 sm:pb-32 lg:flex lg:px-8 lg:py-40">
        <div className="mx-auto max-w-2xl lg:mx-0 lg:max-w-xl lg:flex-shrink-0 lg:pt-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <div className="mt-24 sm:mt-32 lg:mt-16">
              <Link href="/conference-2025" className="inline-flex space-x-6">
                <span className="rounded-full bg-primary/10 px-3 py-1 text-sm font-semibold leading-6 text-primary ring-1 ring-inset ring-primary/10">
                  What's new
                </span>
                <span className="inline-flex items-center space-x-2 text-sm font-medium leading-6 text-muted-foreground">
                  <span>3rd International Conference 2025</span>
                  <ChevronRightIcon className="h-5 w-5 text-muted-foreground" aria-hidden="true" />
                </span>
              </Link>
            </div>
            
            <h1 className="mt-10 text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              Pakistan Nutrition and Dietetic Society
            </h1>
            
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              PNDS is the only professional nutrition and dietetic society in Pakistan with over 900 members. 
              We lead the way to better nutrition through education, training, and research to promote the 
              nutritional well-being of the Pakistani population.
            </p>
            
            <div className="mt-10 flex items-center gap-x-6">
              <Link
                href="/membership"
                className="rounded-md bg-primary px-3.5 py-2.5 text-sm font-semibold text-primary-foreground shadow-sm hover:bg-primary/90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary transition-colors"
              >
                Become a Member
              </Link>
              <Link 
                href="/about" 
                className="text-sm font-semibold leading-6 text-foreground hover:text-primary transition-colors"
              >
                Learn more <span aria-hidden="true">→</span>
              </Link>
            </div>
          </motion.div>
        </div>

        <div className="mx-auto mt-16 flex max-w-2xl sm:mt-24 lg:ml-10 lg:mr-0 lg:mt-0 lg:max-w-none lg:flex-none xl:ml-32">
          <div className="max-w-3xl flex-none sm:max-w-5xl lg:max-w-none">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative"
            >
              <div className="w-[76rem] rounded-md bg-white/5 ring-1 ring-white/10 backdrop-blur">
                <div className="relative rounded-md bg-gradient-to-br from-primary/10 to-accent/10 p-8">
                  <div className="grid grid-cols-2 gap-8 sm:grid-cols-4">
                    {stats.map((stat, index) => (
                      <motion.div
                        key={stat.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                        className="text-center"
                      >
                        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                          <stat.icon className="h-6 w-6 text-primary" aria-hidden="true" />
                        </div>
                        <div className="text-2xl font-bold text-foreground">{stat.value}</div>
                        <div className="text-sm text-muted-foreground">{stat.name}</div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Bottom gradient */}
      <div
        className="absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]"
        aria-hidden="true"
      >
        <div
          className="relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-accent to-primary opacity-20 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"
          style={{
            clipPath:
              'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
          }}
        />
      </div>
    </div>
  )
}
