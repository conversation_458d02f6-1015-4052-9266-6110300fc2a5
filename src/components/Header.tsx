'use client'

import { useState } from 'react'
import { Dialog, DialogPanel } from '@headlessui/react'
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline'
import Image from 'next/image'
import Link from 'next/link'

const navigation = [
  {
    name: 'About',
    href: '/about',
    children: [
      { name: 'About Us', href: '/about' },
      { name: 'PNDS Constitution & Bylaws', href: '/constitution' },
      { name: 'Executive Committee', href: '/executive-committee' },
      { name: 'Annual General Body Meetings', href: '/agbm' },
      { name: 'Code of Ethics', href: '/code-of-ethics' },
      { name: 'International Affiliations', href: '/international-affiliations' },
      { name: 'National Collaborators', href: '/national-collaborators' },
      { name: 'Contact', href: '/contact' },
    ],
  },
  {
    name: 'Membership',
    href: '/membership',
    children: [
      { name: 'Membership Process', href: '/membership' },
      { name: 'Registration Exam', href: '/rdn-exam' },
      { name: 'RD<PERSON>', href: '/rdn-renewal' },
    ],
  },
  {
    name: 'Activities',
    href: '/activities',
    children: [
      { name: 'Continuing Nutrition Education', href: '/cne' },
      { name: 'Webinars', href: '/webinars' },
      { name: 'Workshops/Seminars', href: '/workshops' },
      { name: 'Conferences', href: '/conferences' },
    ],
  },
  {
    name: '3rd International Conference 2025',
    href: '/conference-2025',
    children: [
      { name: 'Call for Abstracts', href: '/call-for-abstracts' },
      { name: 'Call for Workshops', href: '/call-for-workshops' },
      { name: 'Organizing Committee', href: '/organizing-committee' },
      { name: 'Sponsorship Opportunities', href: '/sponsorship' },
      { name: 'Travel Instructions', href: '/travel-instructions' },
    ],
  },
]

export default function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  return (
    <header className="bg-white shadow-sm">
      {/* Top bar */}
      <div className="bg-primary text-primary-foreground">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex h-10 items-center justify-between text-sm">
            <div className="flex items-center space-x-4">
              <span>📧 <EMAIL></span>
              <span>📞 +92-21-*********</span>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/member-login" className="hover:text-accent transition-colors">
                Register/Login as Member
              </Link>
              <Link href="/sponsorship" className="hover:text-accent transition-colors">
                Sponsorship Opportunities
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main header */}
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-20 items-center justify-between">
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-3">
              <div className="h-12 w-12 bg-primary rounded-full flex items-center justify-center">
                <span className="text-primary-foreground font-bold text-xl">P</span>
              </div>
              <div>
                <h1 className="text-xl font-bold text-foreground">PNDS</h1>
                <p className="text-sm text-muted-foreground">Pakistan Nutrition and Dietetic Society</p>
              </div>
            </Link>
          </div>

          {/* Desktop navigation */}
          <nav className="hidden lg:flex lg:space-x-8">
            {navigation.map((item) => (
              <div key={item.name} className="relative group">
                <Link
                  href={item.href}
                  className="text-foreground hover:text-primary transition-colors duration-200 font-medium"
                >
                  {item.name}
                </Link>
                {item.children && (
                  <div className="absolute left-0 mt-2 w-64 bg-white shadow-lg rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                    <div className="py-2">
                      {item.children.map((child) => (
                        <Link
                          key={child.name}
                          href={child.href}
                          className="block px-4 py-2 text-sm text-foreground hover:bg-muted hover:text-primary transition-colors"
                        >
                          {child.name}
                        </Link>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </nav>

          {/* Mobile menu button */}
          <div className="lg:hidden">
            <button
              type="button"
              className="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-foreground"
              onClick={() => setMobileMenuOpen(true)}
            >
              <span className="sr-only">Open main menu</span>
              <Bars3Icon className="h-6 w-6" aria-hidden="true" />
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <Dialog className="lg:hidden" open={mobileMenuOpen} onClose={setMobileMenuOpen}>
        <div className="fixed inset-0 z-50" />
        <DialogPanel className="fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10">
          <div className="flex items-center justify-between">
            <Link href="/" className="-m-1.5 p-1.5">
              <span className="sr-only">PNDS</span>
              <div className="h-8 w-8 bg-primary rounded-full flex items-center justify-center">
                <span className="text-primary-foreground font-bold">P</span>
              </div>
            </Link>
            <button
              type="button"
              className="-m-2.5 rounded-md p-2.5 text-foreground"
              onClick={() => setMobileMenuOpen(false)}
            >
              <span className="sr-only">Close menu</span>
              <XMarkIcon className="h-6 w-6" aria-hidden="true" />
            </button>
          </div>
          <div className="mt-6 flow-root">
            <div className="-my-6 divide-y divide-gray-500/10">
              <div className="space-y-2 py-6">
                {navigation.map((item) => (
                  <div key={item.name}>
                    <Link
                      href={item.href}
                      className="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-foreground hover:bg-muted"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      {item.name}
                    </Link>
                    {item.children && (
                      <div className="ml-4 space-y-1">
                        {item.children.map((child) => (
                          <Link
                            key={child.name}
                            href={child.href}
                            className="-mx-3 block rounded-lg px-3 py-1 text-sm leading-7 text-muted-foreground hover:bg-muted hover:text-foreground"
                            onClick={() => setMobileMenuOpen(false)}
                          >
                            {child.name}
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </DialogPanel>
      </Dialog>
    </header>
  )
}
