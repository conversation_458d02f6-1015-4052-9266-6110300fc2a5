/**
 * @license lucide-react v0.532.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M18 12h2", key: "quuxs7" }],
  ["path", { d: "M18 16h2", key: "zsn3lv" }],
  ["path", { d: "M18 20h2", key: "9x5y9y" }],
  ["path", { d: "M18 4h2", key: "1luxfb" }],
  ["path", { d: "M18 8h2", key: "nxqzg" }],
  ["path", { d: "M4 12h2", key: "1ltxp0" }],
  ["path", { d: "M4 16h2", key: "8a5zha" }],
  ["path", { d: "M4 20h2", key: "27dk57" }],
  ["path", { d: "M4 4h2", key: "10groj" }],
  ["path", { d: "M4 8h2", key: "18vq6w" }],
  [
    "path",
    {
      d: "M8 2a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2h-1.5c-.276 0-.494.227-.562.495a2 2 0 0 1-3.876 0C9.994 2.227 9.776 2 9.5 2z",
      key: "1681fp"
    }
  ]
];
const Microchip = createLucideIcon("microchip", __iconNode);

export { __iconNode, Microchip as default };
//# sourceMappingURL=microchip.js.map
