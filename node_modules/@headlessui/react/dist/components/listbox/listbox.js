"use client";import{useFocusRing as Se}from"@react-aria/focus";import{useHover as Re}from"@react-aria/interactions";import w,{Fragment as ce,createContext as fe,useCallback as Q,useContext as Te,useEffect as Ae,useMemo as V,useRef as oe,useState as _e}from"react";import{flushSync as z}from"react-dom";import{useActivePress as Fe}from'../../hooks/use-active-press.js';import{useByComparator as Ce}from'../../hooks/use-by-comparator.js';import{useControllable as Me}from'../../hooks/use-controllable.js';import{useDefaultValue as we}from'../../hooks/use-default-value.js';import{useDidElementMove as Be}from'../../hooks/use-did-element-move.js';import{useDisposables as Ie}from'../../hooks/use-disposables.js';import{useElementSize as ke}from'../../hooks/use-element-size.js';import{useEvent as _}from'../../hooks/use-event.js';import{useId as ne}from'../../hooks/use-id.js';import{useInertOthers as Ne}from'../../hooks/use-inert-others.js';import{useIsoMorphicEffect as ue}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as Ue}from'../../hooks/use-latest-value.js';import{useOnDisappear as He}from'../../hooks/use-on-disappear.js';import{useOutsideClick as Ge}from'../../hooks/use-outside-click.js';import{useOwnerDocument as be}from'../../hooks/use-owner.js';import{Action as re,useQuickRelease as Ve}from'../../hooks/use-quick-release.js';import{useResolveButtonType as Ke}from'../../hooks/use-resolve-button-type.js';import{useScrollLock as ze}from'../../hooks/use-scroll-lock.js';import{useSyncRefs as $}from'../../hooks/use-sync-refs.js';import{useTextValue as We}from'../../hooks/use-text-value.js';import{useTrackedPointer as Xe}from'../../hooks/use-tracked-pointer.js';import{transitionDataAttributes as je,useTransition as Je}from'../../hooks/use-transition.js';import{useDisabled as Qe}from'../../internal/disabled.js';import{FloatingProvider as $e,useFloatingPanel as qe,useFloatingPanelProps as Ye,useFloatingReference as Ze,useFloatingReferenceProps as et,useResolvedAnchor as tt}from'../../internal/floating.js';import{FormFields as ot}from'../../internal/form-fields.js';import{useFrozenData as nt}from'../../internal/frozen.js';import{useProvidedId as rt}from'../../internal/id.js';import{OpenClosedProvider as lt,State as le,useOpenClosed as at}from'../../internal/open-closed.js';import{stackMachines as it}from'../../machines/stack-machine.js';import{useSlice as B}from'../../react-glue.js';import{isDisabledReactIssue7711 as me}from'../../utils/bugs.js';import{Focus as x}from'../../utils/calculate-active-index.js';import{disposables as st}from'../../utils/disposables.js';import*as pt from'../../utils/dom.js';import{Focus as ye,FocusableMode as ut,focusFrom as dt,isFocusableElement as ct}from'../../utils/focus-management.js';import{attemptSubmit as ft}from'../../utils/form.js';import{match as ae}from'../../utils/match.js';import{getOwnerDocument as Tt}from'../../utils/owner.js';import{RenderFeatures as xe,forwardRefWithAs as q,mergeProps as Oe,useRender as Y}from'../../utils/render.js';import{useDescribedBy as bt}from'../description/description.js';import{Keys as T}from'../keyboard.js';import{Label as mt,useLabelledBy as yt,useLabels as xt}from'../label/label.js';import{MouseButton as Le}from'../mouse.js';import{Portal as Ot}from'../portal/portal.js';import{ActionTypes as Lt,ActivationTrigger as Pe,ListboxStates as d,ValueMode as k}from'./listbox-machine.js';import{ListboxContext as Pt,useListboxMachine as gt,useListboxMachineContext as de}from'./listbox-machine-glue.js';let ie=fe(null);ie.displayName="ListboxDataContext";function Z(g){let D=Te(ie);if(D===null){let O=new Error(`<${g} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(O,Z),O}return D}let Et=ce;function vt(g,D){let O=ne(),u=Qe(),{value:i,defaultValue:l,form:F,name:s,onChange:b,by:n,invalid:c=!1,disabled:m=u||!1,horizontal:a=!1,multiple:t=!1,__demoMode:p=!1,...S}=g;const E=a?"horizontal":"vertical";let N=$(D),C=we(l),[f=t?[]:void 0,L]=Me(i,b,C),y=gt({id:O,__demoMode:p}),I=oe({static:!1,hold:!1}),U=oe(new Map),M=Ce(n),H=Q(h=>ae(r.mode,{[k.Multi]:()=>f.some(j=>M(j,h)),[k.Single]:()=>M(f,h)}),[f]),r=V(()=>({value:f,disabled:m,invalid:c,mode:t?k.Multi:k.Single,orientation:E,onChange:L,compare:M,isSelected:H,optionsPropsRef:I,listRef:U}),[f,m,c,t,E,L,M,H,I,U]);ue(()=>{y.state.dataRef.current=r},[r]);let P=B(y,h=>h.listboxState),G=it.get(null),W=B(G,Q(h=>G.selectors.isTop(h,O),[G,O])),[v,K]=B(y,h=>[h.buttonElement,h.optionsElement]);Ge(W,[v,K],(h,j)=>{y.send({type:Lt.CloseListbox}),ct(j,ut.Loose)||(h.preventDefault(),v==null||v.focus())});let X=V(()=>({open:P===d.Open,disabled:m,invalid:c,value:f}),[P,m,c,f]),[ee,o]=xt({inherit:!0}),R={ref:N},se=Q(()=>{if(C!==void 0)return L==null?void 0:L(C)},[L,C]),te=Y();return w.createElement(o,{value:ee,props:{htmlFor:v==null?void 0:v.id},slot:{open:P===d.Open,disabled:m}},w.createElement($e,null,w.createElement(Pt.Provider,{value:y},w.createElement(ie.Provider,{value:r},w.createElement(lt,{value:ae(P,{[d.Open]:le.Open,[d.Closed]:le.Closed})},s!=null&&f!=null&&w.createElement(ot,{disabled:m,data:{[s]:f},form:F,onReset:se}),te({ourProps:R,theirProps:S,slot:X,defaultTag:Et,name:"Listbox"}))))))}let ht="button";function Dt(g,D){let O=ne(),u=rt(),i=Z("Listbox.Button"),l=de("Listbox.Button"),{id:F=u||`headlessui-listbox-button-${O}`,disabled:s=i.disabled||!1,autoFocus:b=!1,...n}=g,c=$(D,Ze(),l.actions.setButtonElement),m=et(),[a,t,p]=B(l,o=>[o.listboxState,o.buttonElement,o.optionsElement]),S=a===d.Open;Ve(S,{trigger:t,action:Q(o=>{if(t!=null&&t.contains(o.target))return re.Ignore;let R=o.target.closest('[role="option"]:not([data-disabled])');return pt.isHTMLElement(R)?re.Select(R):p!=null&&p.contains(o.target)?re.Ignore:re.Close},[t,p]),close:l.actions.closeListbox,select:l.actions.selectActiveOption});let E=_(o=>{switch(o.key){case T.Enter:ft(o.currentTarget);break;case T.Space:case T.ArrowDown:o.preventDefault(),l.actions.openListbox({focus:i.value?x.Nothing:x.First});break;case T.ArrowUp:o.preventDefault(),l.actions.openListbox({focus:i.value?x.Nothing:x.Last});break}}),N=_(o=>{switch(o.key){case T.Space:o.preventDefault();break}}),C=oe(null),f=_(o=>{var R;if(C.current=o.pointerType,o.pointerType==="mouse"&&o.button===Le.Left){if(me(o.currentTarget))return o.preventDefault();l.state.listboxState===d.Open?(z(()=>l.actions.closeListbox()),(R=l.state.buttonElement)==null||R.focus({preventScroll:!0})):(o.preventDefault(),l.actions.openListbox({focus:x.Nothing}))}}),L=_(o=>{var R;if(C.current!=="mouse"&&o.button===Le.Left){if(me(o.currentTarget))return o.preventDefault();l.state.listboxState===d.Open?(z(()=>l.actions.closeListbox()),(R=l.state.buttonElement)==null||R.focus({preventScroll:!0})):(o.preventDefault(),l.actions.openListbox({focus:x.Nothing}))}}),y=_(o=>o.preventDefault()),I=yt([F]),U=bt(),{isFocusVisible:M,focusProps:H}=Se({autoFocus:b}),{isHovered:r,hoverProps:P}=Re({isDisabled:s}),{pressed:G,pressProps:W}=Fe({disabled:s}),v=V(()=>({open:a===d.Open,active:G||a===d.Open,disabled:s,invalid:i.invalid,value:i.value,hover:r,focus:M,autofocus:b}),[a,i.value,s,r,M,G,i.invalid,b]),K=B(l,o=>o.listboxState===d.Open),X=Oe(m(),{ref:c,id:F,type:Ke(g,t),"aria-haspopup":"listbox","aria-controls":p==null?void 0:p.id,"aria-expanded":K,"aria-labelledby":I,"aria-describedby":U,disabled:s||void 0,autoFocus:b,onKeyDown:E,onKeyUp:N,onKeyPress:y,onPointerDown:f,onClick:L},H,P,W);return Y()({ourProps:X,theirProps:n,slot:v,defaultTag:ht,name:"Listbox.Button"})}let ge=fe(!1),St="div",Rt=xe.RenderStrategy|xe.Static;function At(g,D){let O=ne(),{id:u=`headlessui-listbox-options-${O}`,anchor:i,portal:l=!1,modal:F=!0,transition:s=!1,...b}=g,n=tt(i),[c,m]=_e(null);n&&(l=!0);let a=Z("Listbox.Options"),t=de("Listbox.Options"),[p,S,E,N]=B(t,e=>[e.listboxState,e.buttonElement,e.optionsElement,e.__demoMode]),C=be(S),f=be(E),L=at(),[y,I]=Je(s,c,L!==null?(L&le.Open)===le.Open:p===d.Open);He(y,S,t.actions.closeListbox);let U=N?!1:F&&p===d.Open;ze(U,f);let M=N?!1:F&&p===d.Open;Ne(M,{allowed:Q(()=>[S,E],[S,E])});let H=p!==d.Open,P=Be(H,S)?!1:y,G=y&&p===d.Closed,W=nt(G,a.value),v=_(e=>a.compare(W,e)),K=B(t,e=>{var J;if(n==null||!((J=n==null?void 0:n.to)!=null&&J.includes("selection")))return null;let A=e.options.findIndex(pe=>v(pe.dataRef.current.value));return A===-1&&(A=0),A}),X=(()=>{if(n==null)return;if(K===null)return{...n,inner:void 0};let e=Array.from(a.listRef.current.values());return{...n,inner:{listRef:{current:e},index:K}}})(),[ee,o]=qe(X),R=Ye(),se=$(D,n?ee:null,t.actions.setOptionsElement,m),te=Ie();Ae(()=>{var A;let e=E;e&&p===d.Open&&e!==((A=Tt(e))==null?void 0:A.activeElement)&&(e==null||e.focus({preventScroll:!0}))},[p,E]);let h=_(e=>{var A,J;switch(te.dispose(),e.key){case T.Space:if(t.state.searchQuery!=="")return e.preventDefault(),e.stopPropagation(),t.actions.search(e.key);case T.Enter:if(e.preventDefault(),e.stopPropagation(),t.state.activeOptionIndex!==null){let{dataRef:pe}=t.state.options[t.state.activeOptionIndex];t.actions.onChange(pe.current.value)}a.mode===k.Single&&(z(()=>t.actions.closeListbox()),(A=t.state.buttonElement)==null||A.focus({preventScroll:!0}));break;case ae(a.orientation,{vertical:T.ArrowDown,horizontal:T.ArrowRight}):return e.preventDefault(),e.stopPropagation(),t.actions.goToOption({focus:x.Next});case ae(a.orientation,{vertical:T.ArrowUp,horizontal:T.ArrowLeft}):return e.preventDefault(),e.stopPropagation(),t.actions.goToOption({focus:x.Previous});case T.Home:case T.PageUp:return e.preventDefault(),e.stopPropagation(),t.actions.goToOption({focus:x.First});case T.End:case T.PageDown:return e.preventDefault(),e.stopPropagation(),t.actions.goToOption({focus:x.Last});case T.Escape:e.preventDefault(),e.stopPropagation(),z(()=>t.actions.closeListbox()),(J=t.state.buttonElement)==null||J.focus({preventScroll:!0});return;case T.Tab:e.preventDefault(),e.stopPropagation(),z(()=>t.actions.closeListbox()),dt(t.state.buttonElement,e.shiftKey?ye.Previous:ye.Next);break;default:e.key.length===1&&(t.actions.search(e.key),te.setTimeout(()=>t.actions.clearSearch(),350));break}}),j=B(t,e=>{var A;return(A=e.buttonElement)==null?void 0:A.id}),Ee=V(()=>({open:p===d.Open}),[p]),ve=Oe(n?R():{},{id:u,ref:se,"aria-activedescendant":B(t,t.selectors.activeDescendantId),"aria-multiselectable":a.mode===k.Multi?!0:void 0,"aria-labelledby":j,"aria-orientation":a.orientation,onKeyDown:h,role:"listbox",tabIndex:p===d.Open?0:void 0,style:{...b.style,...o,"--button-width":ke(S,!0).width},...je(I)}),he=Y(),De=V(()=>a.mode===k.Multi?a:{...a,isSelected:v},[a,v]);return w.createElement(Ot,{enabled:l?g.static||y:!1,ownerDocument:C},w.createElement(ie.Provider,{value:De},he({ourProps:ve,theirProps:b,slot:Ee,defaultTag:St,features:Rt,visible:P,name:"Listbox.Options"})))}let _t="div";function Ft(g,D){let O=ne(),{id:u=`headlessui-listbox-option-${O}`,disabled:i=!1,value:l,...F}=g,s=Te(ge)===!0,b=Z("Listbox.Option"),n=de("Listbox.Option"),c=B(n,r=>n.selectors.isActive(r,u)),m=b.isSelected(l),a=oe(null),t=We(a),p=Ue({disabled:i,value:l,domRef:a,get textValue(){return t()}}),S=$(D,a,r=>{r?b.listRef.current.set(u,r):b.listRef.current.delete(u)}),E=B(n,r=>n.selectors.shouldScrollIntoView(r,u));ue(()=>{if(E)return st().requestAnimationFrame(()=>{var r,P;(P=(r=a.current)==null?void 0:r.scrollIntoView)==null||P.call(r,{block:"nearest"})})},[E,a]),ue(()=>{if(!s)return n.actions.registerOption(u,p),()=>n.actions.unregisterOption(u)},[p,u,s]);let N=_(r=>{var P;if(i)return r.preventDefault();n.actions.onChange(l),b.mode===k.Single&&(z(()=>n.actions.closeListbox()),(P=n.state.buttonElement)==null||P.focus({preventScroll:!0}))}),C=_(()=>{if(i)return n.actions.goToOption({focus:x.Nothing});n.actions.goToOption({focus:x.Specific,id:u})}),f=Xe(),L=_(r=>{f.update(r),!i&&(c||n.actions.goToOption({focus:x.Specific,id:u},Pe.Pointer))}),y=_(r=>{f.wasMoved(r)&&(i||c||n.actions.goToOption({focus:x.Specific,id:u},Pe.Pointer))}),I=_(r=>{f.wasMoved(r)&&(i||c&&n.actions.goToOption({focus:x.Nothing}))}),U=V(()=>({active:c,focus:c,selected:m,disabled:i,selectedOption:m&&s}),[c,m,i,s]),M=s?{}:{id:u,ref:S,role:"option",tabIndex:i===!0?void 0:-1,"aria-disabled":i===!0?!0:void 0,"aria-selected":m,disabled:void 0,onClick:N,onFocus:C,onPointerEnter:L,onMouseEnter:L,onPointerMove:y,onMouseMove:y,onPointerLeave:I,onMouseLeave:I},H=Y();return!m&&s?null:H({ourProps:M,theirProps:F,slot:U,defaultTag:_t,name:"Listbox.Option"})}let Ct=ce;function Mt(g,D){let{options:O,placeholder:u,...i}=g,F={ref:$(D)},s=Z("ListboxSelectedOption"),b=V(()=>({}),[]),n=s.value===void 0||s.value===null||s.mode===k.Multi&&Array.isArray(s.value)&&s.value.length===0,c=Y();return w.createElement(ge.Provider,{value:!0},c({ourProps:F,theirProps:{...i,children:w.createElement(w.Fragment,null,u&&n?u:O)},slot:b,defaultTag:Ct,name:"ListboxSelectedOption"}))}let wt=q(vt),Bt=q(Dt),It=mt,kt=q(At),Nt=q(Ft),Ut=q(Mt),Io=Object.assign(wt,{Button:Bt,Label:It,Options:kt,Option:Nt,SelectedOption:Ut});export{Io as Listbox,Bt as ListboxButton,It as ListboxLabel,Nt as ListboxOption,kt as ListboxOptions,Ut as ListboxSelectedOption};
