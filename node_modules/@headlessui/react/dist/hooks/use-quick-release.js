import{useRef as o}from"react";import*as m from'../utils/dom.js';import{useDocumentEvent as E}from'./use-document-event.js';var H=(t=>(t[t.Ignore=0]="Ignore",t[t.Select=1]="Select",t[t.Close=2]="Close",t))(H||{});const L={Ignore:{kind:0},Select:r=>({kind:1,target:r}),Close:{kind:2}},g=200,d=5;function R(r,{trigger:n,action:f,close:t,select:T}){let l=o(null),i=o(null),u=o(null);E(r&&n!==null,"pointerdown",e=>{m.isNode(e==null?void 0:e.target)&&n!=null&&n.contains(e.target)&&(i.current=e.x,u.current=e.y,l.current=new Date)}),E(r&&n!==null,"pointerup",e=>{var a,s;if(l.current===null||!m.isHTMLorSVGElement(e.target)||Math.abs(e.x-((a=i.current)!=null?a:e.x))<d&&Math.abs(e.y-((s=u.current)!=null?s:e.y))<d)return;let c=f(e),M=new Date().getTime()-l.current.getTime();switch(l.current=null,c.kind){case 0:return;case 1:{M>g&&(T(c.target),t());break}case 2:{t();break}}},{capture:!0})}export{L as Action,R as useQuickRelease};
